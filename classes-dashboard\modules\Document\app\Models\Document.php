<?php

namespace Document\Models;

use Admission\Models\StudentDetails;
use Illuminate\Database\Eloquent\Model;

class Document extends Model
{
    protected $table = 'documents';

    protected $fillable = [
        'document_name',
        'file',
        'category_id',
        'student_id',
        'other_category',
        'created_by',
        'description',
        'class_uuid',
    ];

    public function category()
    {
        return $this->belongsTo(DocumentCategories::class, 'category_id');
    }
    
    public function student()
    {
        return $this->belongsTo(StudentDetails::class, 'student_id');
    }

}