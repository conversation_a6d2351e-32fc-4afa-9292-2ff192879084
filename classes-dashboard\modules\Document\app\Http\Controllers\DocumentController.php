<?php

namespace Document\Http\Controllers;

use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\Controller;
use Document\Repositories\DocumentRepository;
use Document\Http\Requests\CreateDocumentRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Department\Models\Department;

class DocumentController extends Controller
{
    protected $documentRepository;

    public function __construct(DocumentRepository $documentRepository)
    {
        $this->middleware('permission:read document', ['only' => ['index']]);
        $this->middleware('permission:create document', ['only' => ['create', 'store']]);
        $this->middleware('permission:delete document', ['only' => ['destroy']]);

        $this->documentRepository = $documentRepository;
    }

    public function index(Request $request)
    {
        if (request()->ajax()) {
            $list = $this->documentRepository->getAll($request);
            return $this->documentRepository->getDatatable($list);
        }
        $department = Department::all();
        return view('Document::index', compact('department')); 
    }

    public function create(Request $request)
    {
        $studentId = $request->student_id;
        if (!$studentId && request()->is('student/*')) {
            abort(400, 'Student ID is required for this action.');
        }
        return view('Document::create', compact('studentId'));
    }

    public function store(CreateDocumentRequest $request)
    {
        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $subdomain = request()->getHost();
            $fileName = $file->getClientOriginalName();
            $file->storeAs("public/{$subdomain}/documents", $fileName);

            $studentId = $request->student_id;
            if (!$studentId) {
                return response()->json(['error' => 'Student ID is required.'], 400);
            }

            $data = [
                'document_name' => $request->document_name,
                'file' => $fileName,
                'category_id' => $request->category_id,
                'student_id' => $studentId,
                'other_category' => $request->category_id == 13 ? $request->other_category : null,
                'description' => $request->description,
                'created_by' => Auth::user()->first_name . " " . Auth::user()->last_name,
                'class_uuid' => Auth::id(),
            ];

            $document = $this->documentRepository->storeDocument($data);
            if ($document) {
                return response()->json(['success' => 'Document Added Successfully!!']);
            }
        }
        return response()->json(['error' => 'Failed to upload file.'], 400);
    }

    public function destroy($id)
    {
        $document = $this->documentRepository->getDocumentById($id);
        if (!$document) {
            return response()->json(['error' => 'Document not found.'], 404);
        }

        $subdomain = tenantData('subdomain');
        Storage::delete("public/{$subdomain}/documents/" . $document->file);
        $document->delete();

        return response()->json(['success' => 'Document deleted successfully!!']);
    }

    public function download($id)
    {
        $document = $this->documentRepository->getDocumentById($id);
        if (!$document) {
            return response()->json(['error' => 'Document not found.'], 404);
        }

        $subdomain = tenantData('subdomain');
        $pathToFile = storage_path("app/public/{$subdomain}/documents/" . $document->file);
        if (!file_exists($pathToFile)) {
            return response()->json(['error' => 'File not found.'], 404);
        }

        return response()->download($pathToFile);
    }
}