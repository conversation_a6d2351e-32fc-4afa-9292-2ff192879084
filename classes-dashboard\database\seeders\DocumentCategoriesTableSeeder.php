<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\Classes;

class DocumentCategoriesTableSeeder extends Seeder
{
    /**
     * Run the seeder.
     *
     * @return void
     */
    public function run()
    {
        $categoryNames = [
            'Birth Certificate',
            'Adhaar Card',
            'Passport',
            'Address Proof',
            'Bonafide Certificate',
            'Medical Records',
            'Allergy/Condition Reports',
            'Parental/Guardian Documents',
            'ID Proof (Parent/Guardian)',
            'Income Certificate',
            'Caste/Category Certificate',
            'Fees',
            'Others',
        ];

        // Get all existing classes
        $classes = Classes::all();

        if ($classes->isEmpty()) {
            // If no classes exist, create a default class UUID for seeding
            $defaultClassUuid = '00000000-0000-0000-0000-000000000000';

            $categories = [];
            foreach ($categoryNames as $categoryName) {
                $categories[] = [
                    'category_name' => $categoryName,
                    'class_uuid' => $defaultClassUuid,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }

            DB::table('document_categories')->insert($categories);
        } else {
            // Create categories for each existing class
            $categories = [];
            foreach ($classes as $class) {
                foreach ($categoryNames as $categoryName) {
                    $categories[] = [
                        'category_name' => $categoryName,
                        'class_uuid' => $class->id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }
            }

            DB::table('document_categories')->insert($categories);
        }
    }
}