<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Department\Models\Department;

class DocumentCategoriesTableSeeder extends Seeder
{
    /**
     * Run the seeder.
     *
     * @return void
     */
    public function run()
    {
        // Get UUID from Department table (created by SchoolConfigSeeder)
        $department = Department::first();
        $classUuid = $department ? $department->class_uuid : 'system-uuid-' . uniqid();

        $categories = [
            ['category_name' => 'Birth Certificate', 'class_uuid' => $classUuid],
            ['category_name' => 'Adhaar Card', 'class_uuid' => $classUuid],
            ['category_name' => 'Passport', 'class_uuid' => $classUuid],
            ['category_name' => 'Address Proof', 'class_uuid' => $classUuid],
            ['category_name' => 'Bonafide Certificate', 'class_uuid' => $classUuid],
            ['category_name' => 'Medical Records', 'class_uuid' => $classUuid],
            ['category_name' => 'Allergy/Condition Reports', 'class_uuid' => $classUuid],
            ['category_name' => 'Parental/Guardian Documents', 'class_uuid' => $classUuid],
            ['category_name' => 'ID Proof (Parent/Guardian)', 'class_uuid' => $classUuid],
            ['category_name' => 'Income Certificate', 'class_uuid' => $classUuid],
            ['category_name' => 'Caste/Category Certificate', 'class_uuid' => $classUuid],
            ['category_name' => 'Fees', 'class_uuid' => $classUuid],
            ['category_name' => 'Others', 'class_uuid' => $classUuid],
        ];

        // Insert data into the document_categories table
        DB::table('document_categories')->insert($categories);
    }
}