@extends('layouts.app')
@section('content')
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-12 main-title-flex">
                    <h1>Edit Fees Structure</h1>
                </div>
            </div>
        </div>
    </div>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="card card-primary">
                    <div class="card-header">
                        <h3 class="card-title" style="color:white !important">Classroom Details</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <label class="col-form-label"><b>Classroom Name : </b> {{ $classroom->class_name }}</label>
                        </div>
                        <div class="row">
                            <label class="col-form-label"><b>Department : </b>
                                {{ $classroom->department_name->name }}</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div class="card card-primary" id="fees-structure">
                    <div class="card-header">
                        <h3 class="card-title" style="color:white !important">Fees Structure (Per Month)</h3>
                    </div>
                    <div class="card-body">
                        <div class="generate-buttons">
                            <button id="addCategory" class="btn btn-primary">Add Category to Fee</button>
                            <a target="_blank" href="{{ route('feesmanager.category.index') }}"
                                class="btn btn-primary">Create New Categories</a>
                        </div>
                        <form id="salaryInstallmentForm">
                            @csrf
                            <input type="hidden" name="class_id" value="{{ $classroom->id }}" />
                            <input type="hidden" name="year" id="year"
                                value="{{ isset($feesdetail) ? $feesdetail->year_id : '' }}" />
                            <div>
                                <table id="classroomWiseFee_table"
                                    class="table display table-striped table-borderless dt-responsive">
                                    <thead>
                                        <tr>
                                            <th class="bg-info">Fee Categories</th>
                                            @if (isset($feesdetail))
                                                @foreach ($feesdetail->getCategoryFees as $fee)
                                                    <th class="bg-info category-header"
                                                        data-category-id="{{ $fee->category_id }}">
                                                        {{ $fee->category->category_name }}
                                                        <span class="remove-category"
                                                            style="cursor: pointer; color: red;">×</span>
                                                    </th>
                                                @endforeach
                                            @endif
                                        </tr>
                                    </thead>
                                    <tbody id="feeBody">
                                        <tr>
                                            <td>Fees</td>
                                            @if (isset($feesdetail))
                                                @foreach ($feesdetail->getCategoryFees as $fee)
                                                    <td>
                                                        <input type="number" value="{{ $fee->amount }}"
                                                            name="category_{{ $fee->category_id }}" required max="9999999"
                                                            placeholder="Add Fee" class="form-control total"
                                                            step="0.01" />
                                                    </td>
                                                @endforeach
                                            @endif
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="modal fade" id="categoryModal" tabindex="-1" role="dialog"
                                aria-labelledby="categoryModalLabel" aria-hidden="true">
                                <div class="modal-dialog" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="categoryModalLabel">Select Categories</h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">×</span>
                                            </button>
                                        </div>
                                        <div class="modal-body">
                                            <select id="categorySelect" class="form-control" multiple="multiple">
                                                @foreach ($category as $cat)
                                                    <option value="{{ $cat->id }}">{{ $cat->category_name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary"
                                                data-dismiss="modal">Close</button>
                                            <button type="button" class="btn btn-primary" id="saveCategories">Save</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @if (!isset($feesdetail) || $feesdetail->lock != 1)
                                <div class="float-right">
                                    <h3 class="totalfees">Total Fees: <span id="totalAmount">0</span></h3>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Save Details
                                </button>
                                <a href="{{ route('feesmanager.classroom-wise-fee.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-undo"></i> Reset
                                </a>
                                @if (isset($feesdetail) && $feesdetail->lock != 1)
                                    <button data-classid="{{ $classroom->id }}" class="btn btn-danger lockFees">
                                        <i class="fas fa-lock"></i> Lock Details
                                    </button>
                                @else
                                    <button data-classid="{{ $classroom->id }}" class="btn btn-warning lockFees">
                                        <i class="fas fa-lock"></i> Unlock Details
                                    </button>
                                @endif
                            @endif
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
@section('scripts')
    <script>
        $(document).ready(function() {
            $('#categorySelect').select2({
                placeholder: "Select categories",
                width: '100%',
                multiple: true
            });

            let selectedCategories = [];
            $('.category-header').each(function() {
                selectedCategories.push($(this).data('category-id').toString());
            });
            $('#categorySelect').val(selectedCategories).trigger('change');

            setTimeout(() => {
                $('.total').trigger('change');
            }, 1000);

            $('#addCategory').click(function(event) {
                event.preventDefault();
                $('#categoryModal').modal('show');
            });

            $('#saveCategories').click(function() {
                let newlySelected = $('#categorySelect').val() || [];

                // Add new categories
                newlySelected.forEach(function(categoryId) {
                    if (!selectedCategories.includes(categoryId)) {
                        let categoryName = $('#categorySelect option[value="' + categoryId + '"]')
                            .text();
                        selectedCategories.push(categoryId);

                        let headerRow = $('#classroomWiseFee_table thead tr');
                        headerRow.append(
                            `<th class="bg-info category-header" data-category-id="${categoryId}">${categoryName} <span class="remove-category" style="cursor: pointer; color: red;">×</span></th>`
                        );

                        let feeRow = $('#feeBody tr');
                        feeRow.append(
                            `<td><input type="number" value="0" name="category_${categoryId}" required max="9999999" placeholder="Add Fee" class="form-control total" step="0.01" /></td>`
                        );
                    }
                });

                // Remove unselected categories
                selectedCategories.slice().forEach(function(categoryId) {
                    if (!newlySelected.includes(categoryId)) {
                        let index = selectedCategories.indexOf(categoryId);
                        selectedCategories.splice(index, 1);
                        let header = $(`th[data-category-id="${categoryId}"]`);
                        let columnIndex = header.index() + 1;
                        header.remove();
                        $('#feeBody tr').find(`td:nth-child(${columnIndex})`).remove();
                    }
                });

                $('#categorySelect').val(selectedCategories).trigger('change');
                $('#categoryModal').modal('hide');
                $('.total').trigger('change');
            });

            $(document).on('click', '.remove-category', function() {
                let header = $(this).parent();
                let categoryId = header.data('category-id');
                let index = selectedCategories.indexOf(categoryId.toString());
                if (index > -1) {
                    selectedCategories.splice(index, 1);
                }
                let columnIndex = header.index() + 1;
                header.remove();
                $('#feeBody tr').find(`td:nth-child(${columnIndex})`).remove();
                $('#categorySelect').val(selectedCategories).trigger('change');
                $('.total').trigger('change');
            });

            $(document).on('change', '.total', function() {
                let totalFeesAmount = 0;
                $('#feeBody tr').find('td input.total').each(function() {
                    let value = parseFloat($(this).val());
                    if (!isNaN(value)) {
                        totalFeesAmount += value;
                    }
                });
                $('#totalAmount').text(totalFeesAmount.toFixed(2));
            });

            $('#salaryInstallmentForm').submit(function(event) {
                event.preventDefault();

                let totalFeesAmount = parseFloat($('#totalAmount').text());
                if (totalFeesAmount <= 0) {
                    toastr.error("Fees should be more than 0");
                    return;
                }

                let params = $.extend({}, doAjax_params_default);
                params["requestType"] = "POST";
                params["url"] = "{{ route('feesmanager.classroom-wise-fee.store') }}";
                params["data"] = $(this).serialize();

                params["successCallbackFunction"] = function(result) {
                    toastr.success(result.success);
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                };
                commonAjax(params);
            });

            $(".readonly").keydown(function(e) {
                e.preventDefault();
            });

            $(document).on("click", ".lockFees", function(e) {
                e.preventDefault();
                var params = $.extend({}, doAjax_params_default);
                params["url"] = "{{ route('feesmanager.lockfees', $classroom->id) }}";
                params["requestType"] = "POST";
                params["successCallbackFunction"] = function(result) {
                    toastr.success(result.success);
                };

                let calert = function() {
                    commonAjax(params);
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                };
                commonAlert(calert);
            });
        });
    </script>
@endsection
