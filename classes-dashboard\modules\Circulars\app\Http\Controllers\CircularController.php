<?php

namespace Circulars\Http\Controllers;

use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\Controller;
use Circulars\Repositories\CircularRepository;
use Circulars\Http\Requests\CreateCircularRequest;
use Illuminate\Http\Request;

class CircularController extends Controller
{
    protected $circularRepository;

    public function __construct(CircularRepository $circularRepository)
    {
        $this->middleware('permission:read circular', ['only' => ['index']]);
        $this->middleware('permission:create circular', ['only' => ['create', 'store']]);
        $this->middleware('permission:delete circular', ['only' => ['delete','destroy']]);
        $this->circularRepository = $circularRepository;
    }
   
    public function index(Request $request)
    {
        if (request()->ajax()) {
            $list = $this->circularRepository->getAll($request);
            return $this->circularRepository->getDatatable($list);
        }
      return view('Circulars::index');
    }

    public function create()
    {
        return view('Circulars::create');
    }

    public function store(CreateCircularRequest $request)
    {
        $this->circularRepository->storeCircular($request);
        return response()->json(['success' => 'Circular Added Successfully!!']);
    }

    public function destroy($id)
    {
        $circular = $this->circularRepository->getCircularById($id);
        $subdomain = tenantData('subdomain');
        Storage::delete("public/{$subdomain}/circulars/" . $circular->file);
        $circular->delete($id);
        return response()->json(['success' => 'Circular deleted successfully!!']);
    }

    public function download($id)
    {
        $circular = $this->circularRepository->getCircularById($id);
        if (!$circular) {
            return response()->json(['error' => 'Circular not found.'], 404);
        }

        // Try multiple subdomain approaches
        $subdomains = [
            tenantData('subdomain'),
            request()->getHost(),
            'localhost',
            '127.0.0.1'
        ];

        foreach ($subdomains as $subdomain) {
            if ($subdomain) {
                $pathToFile = storage_path("app/public/{$subdomain}/circulars/" . $circular->file);
                if (file_exists($pathToFile)) {
                    return response()->download($pathToFile);
                }
            }
        }

        // Try without subdomain
        $pathToFile = storage_path("app/public/circulars/" . $circular->file);
        if (file_exists($pathToFile)) {
            return response()->download($pathToFile);
        }

        return response()->json(['error' => 'File not found.'], 404);
    }
}
