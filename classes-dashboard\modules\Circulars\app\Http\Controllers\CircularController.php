<?php

namespace Circulars\Http\Controllers;

use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\Controller;
use Circulars\Repositories\CircularRepository;
use Circulars\Http\Requests\CreateCircularRequest;
use Illuminate\Http\Request;

class CircularController extends Controller
{
    protected $circularRepository;

    public function __construct(CircularRepository $circularRepository)
    {
        $this->middleware('permission:read circular', ['only' => ['index']]);
        $this->middleware('permission:create circular', ['only' => ['create', 'store']]);
        $this->middleware('permission:delete circular', ['only' => ['delete','destroy']]);
        $this->circularRepository = $circularRepository;
    }
   
    public function index(Request $request)
    {
        if (request()->ajax()) {
            $list = $this->circularRepository->getAll($request);
            return $this->circularRepository->getDatatable($list);
        }
      return view('Circulars::index');
    }

    public function create()
    {
        return view('Circulars::create');
    }

    public function store(CreateCircularRequest $request)
    {
        $this->circularRepository->storeCircular($request);
        return response()->json(['success' => 'Circular Added Successfully!!']);
    }

    public function destroy($id)
    {
        $circular = $this->circularRepository->getCircularById($id);
        $subdomain = tenantData('subdomain');
        Storage::delete("public/{$subdomain}/circulars/" . $circular->file);
        $circular->delete($id);
        return response()->json(['success' => 'Circular deleted successfully!!']);
    }

    public function download($id)
    {
        $circular = $this->circularRepository->getCircularById($id);
        $subdomain = tenantData('subdomain');

        $pathToFile = storage_path("app/public/{$subdomain}/circulars/" . $circular->file);

        if (!file_exists($pathToFile)) {
            $alternativePaths = [
                storage_path("app/public/circulars/" . $circular->file),
                storage_path("app/public/localhost/circulars/" . $circular->file),
            ];

            foreach ($alternativePaths as $altPath) {
                if (file_exists($altPath)) {
                    return response()->download($altPath);
                }
            }

            return response()->json(['error' => 'File not found.'], 404);
        }

        return response()->download($pathToFile);
    }
}
