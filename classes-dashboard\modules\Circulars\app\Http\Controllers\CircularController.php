<?php

namespace Circulars\Http\Controllers;

use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\Controller;
use Circulars\Repositories\CircularRepository;
use Circulars\Http\Requests\CreateCircularRequest;
use Illuminate\Http\Request;

class CircularController extends Controller
{
    protected $circularRepository;

    public function __construct(CircularRepository $circularRepository)
    {
        $this->middleware('permission:read circular', ['only' => ['index']]);
        $this->middleware('permission:create circular', ['only' => ['create', 'store']]);
        $this->middleware('permission:delete circular', ['only' => ['delete','destroy']]);
        $this->circularRepository = $circularRepository;
    }
   
    public function index(Request $request)
    {
        if (request()->ajax()) {
            $list = $this->circularRepository->getAll($request);
            return $this->circularRepository->getDatatable($list);
        }
      return view('Circulars::index');
    }

    public function create()
    {
        return view('Circulars::create');
    }

    public function store(CreateCircularRequest $request)
    {
        $this->circularRepository->storeCircular($request);
        return response()->json(['success' => 'Circular Added Successfully!!']);
    }

    public function destroy($id)
    {
        $circular = $this->circularRepository->getCircularById($id);
        $subdomain = tenantData('subdomain');
        Storage::delete("public/{$subdomain}/circulars/" . $circular->file);
        $circular->delete($id);
        return response()->json(['success' => 'Circular deleted successfully!!']);
    }

    public function download($id)
    {
        $circular = $this->circularRepository->getCircularById($id);
        if (!$circular) {
            return response()->json(['error' => 'Circular not found.'], 404);
        }

        $subdomain = tenantData('subdomain') ?: request()->getHost();
        $pathToFile = storage_path("app/public/{$subdomain}/circulars/" . $circular->file);

        // Debug information
        $debugInfo = [
            'circular_id' => $id,
            'circular_file' => $circular->file,
            'subdomain' => $subdomain,
            'tenant_subdomain' => tenantData('subdomain'),
            'request_host' => request()->getHost(),
            'primary_path' => $pathToFile,
            'primary_exists' => file_exists($pathToFile),
            'storage_base' => storage_path('app/public'),
        ];

        if (!file_exists($pathToFile)) {
            // Try alternative paths
            $alternativePaths = [
                storage_path("app/public/" . request()->getHost() . "/circulars/" . $circular->file),
                storage_path("app/public/circulars/" . $circular->file),
                storage_path("app/public/localhost/circulars/" . $circular->file),
                storage_path("app/public/127.0.0.1/circulars/" . $circular->file),
            ];

            $debugInfo['alternative_paths'] = [];
            foreach ($alternativePaths as $altPath) {
                $debugInfo['alternative_paths'][] = [
                    'path' => $altPath,
                    'exists' => file_exists($altPath)
                ];
                if (file_exists($altPath)) {
                    return response()->download($altPath);
                }
            }

            // Return debug info instead of generic error
            return response()->json([
                'error' => 'File not found.',
                'debug' => $debugInfo
            ], 404);
        }

        return response()->download($pathToFile);
    }
}
