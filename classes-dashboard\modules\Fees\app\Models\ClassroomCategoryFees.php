<?php

namespace Fees\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ClassroomCategoryFees extends Model
{
    use HasFactory;
    public $table = 'classroom_category_fees_details';

    protected $fillable = [
        'classroom_fees_details_id',
        'category_id',
        'amount',
    ];

        public function category()
    {
        return $this->belongsTo(Category::class, 'category_id');
    }
}
