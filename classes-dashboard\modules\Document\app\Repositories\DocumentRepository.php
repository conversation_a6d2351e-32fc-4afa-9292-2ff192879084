<?php

namespace Document\Repositories;

use Document\Interfaces\DocumentInterface;
use Document\Models\Document;
use Admission\Models\StudentDetails;
use Admission\Models\StudentAcademicInfo;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DocumentRepository implements DocumentInterface
{
    protected $document;
    protected $studentDetails;
    protected $studentAcademicInfo;

    public function __construct(Document $document, StudentDetails $studentDetails, StudentAcademicInfo $studentAcademicInfo)
    {
        $this->document = $document;
        $this->studentDetails = $studentDetails;
        $this->studentAcademicInfo = $studentAcademicInfo;
    }

    public function getAll($request)
    {
        $documents = $this->document
            ->leftJoin('student_details as student_info', 'documents.student_id', '=', 'student_info.id')
            ->leftJoin('document_categories as doc_categories', 'documents.category_id', '=', 'doc_categories.id')
            ->select(
                'documents.*',
                'student_info.first_name',
                'student_info.last_name',
                'doc_categories.category_name',
                DB::raw("LOWER(student_info.first_name) || ' ' || LOWER(COALESCE(student_info.last_name, '')) as student_name"),
                DB::raw("
                    CASE
                        WHEN documents.category_id = 13 AND documents.other_category IS NOT NULL THEN
                            COALESCE(doc_categories.category_name, 'Others') || ' (' || documents.other_category || ')'
                        WHEN documents.category_id = 13 AND doc_categories.category_name IS NULL THEN
                            'Others (' || documents.other_category || ')'
                        ELSE
                            COALESCE(doc_categories.category_name, '-')
                    END as category
                ")
            );

        $documents->where('documents.class_uuid', Auth::id());

        $documents->when($request->student_id, function ($query, $studentId) {
            return $query->where('documents.student_id', $studentId);
        });

        $documents->when($request->department, function ($query, $department) {
            return $query->whereHas('student.getAcademicInfo', function ($q) use ($department) {
                $q->where('department', $department);
            });
        });

        $documents->when($request->classroom, function ($query, $classroom) {
            return $query->whereHas('student.getAcademicInfo', function ($q) use ($classroom) {
                $q->where('classroom', $classroom);
            });
        });

        searchColumn($request->input('columns'), $documents);
        orderColumn($request, $documents, 'documents.id');

        return $documents;
    }

    public function getDatatable($list)
    {
        return datatables()->of($list)
            ->addColumn('title', function ($data) {
                return $data->document_name;
            })
            ->addColumn('file', function ($data) {
                return '<a href="' . route('documents.download', $data->id) . '">' . $data->file . '</a>';
            }) 
            ->addColumn('description', function ($data) {
                return $data->description ?? '-';
            })
            ->addColumn('created_by', function ($data) {
                return $data->created_by;
            })
            ->addColumn('student_name', function ($data) {
                return $data->student_name ?? '-';
            })
            ->addColumn('action', function ($data) {
                $button = '';
                $button .= '<button type="button" class="deleteDocumentEntry btn" title="Delete" data-documentid="' . $data->id . '"><i class="fa fa-trash"></i></button>';
                return $button;
            })
            ->rawColumns(['file', 'action'])
            ->make(true);
    }

    public function storeDocument($data)
    {
        $document = $this->document::create($data);
        return $document;
    }

    public function getDocumentById($id)
    {
        return $this->document::find($id);
    }
}
