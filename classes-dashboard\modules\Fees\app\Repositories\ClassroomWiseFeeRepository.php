<?php

namespace Fees\Repositories;

use Classroom\Models\Classroom;
use Fees\Models\ClassroomFeesDetails;
use Fees\Models\InstallmentDetailsFees;
use Fees\Interfaces\ClassroomWiseFeeInterface;
use Fees\Models\ClassroomCategoryFees;

class ClassroomWiseFeeRepository implements ClassroomWiseFeeInterface
{
    protected $classroom;
    protected $classroomFeesDetails;
    protected $classroomCategoryFees;
    function __construct(
        Classroom $classroom,
        ClassroomFeesDetails $classroomFeesDetails,
        ClassroomCategoryFees $classroomCategoryFees
    ) {
        $this->classroomFeesDetails = $classroomFeesDetails;
        $this->classroom = $classroom;
        $this->classroomCategoryFees = $classroomCategoryFees;
    }

    public function getDatatable($list)
    {
        return datatables()->of($list)
            ->addColumn('class_name', function ($data) {
                return $data->class_name;
            })
            ->addColumn('department_name', function ($data) {
                return $data->department_name;
            })
            ->addColumn('fees_created', function ($data) {
                $sgstatus = $this->classroomFeesDetails::where('classroom_id', $data->id)->where('year_id', getActiveYearId())->first();
                if ($sgstatus > '0') {
                    $salary = '<img title="Generated" style="width: 30px !important;height: 1% !important;" src="../../images/true-icon.png">';
                } else {
                    $salary = '<img title="Not Generated" style="width: 30px !important;height: 1% !important;" src="../../images/que-icon.png">';
                }
                return $salary;
            })
            ->addColumn('action', function ($data) {
                $button = '<a href="' . route('feesmanager.classroom-wise-fee.edit', $data->id) . '?year=' . getActiveYearId() . '" title="Edit" data-target="#newClassroomEntry" class="btn"><i class="fas fa-edit"></i></a>';
                return $button;
            })->rawColumns(['fees_created', 'action'])
            ->make(true);
    }

public function store($data)
    {
        $data['year'] = getActiveYearId();

        $this->classroomFeesDetails
            ->where('classroom_id', $data['class_id'])
            ->where('year_id', $data['year'])
            ->delete();

        $classroomDetail = $this->classroomFeesDetails::create([
            'classroom_id' => $data['class_id'],
            'year_id' => $data['year'],
            'lock' => 0
        ]);

        foreach ($data as $fieldName => $fieldValue) {
            if (strpos($fieldName, 'category_') === 0) {
                $categoryId = str_replace('category_', '', $fieldName);
                $amount = floatval($fieldValue);
                if ($amount > 0 && is_numeric($categoryId)) {
                    $this->classroomCategoryFees::create([
                        'classroom_fees_details_id' => $classroomDetail->id,
                        'category_id' => $categoryId,
                        'amount' => $amount,
                    ]);
                }
            }
        }
    }


    public function getById($id)
    {
        return $this->classroomFeesDetails::with('getInstallment')->find($id);
    }

    public function getFeesByClassroom($id)
    {
        return $this->classroomFeesDetails::where('classroom_id', $id)->where('year_id', getActiveYearId())->first();
    }

    public function lock($id)
    {
        $classroom = $this->classroomFeesDetails::where('classroom_id', $id)->first();
        $classroom->lock = !$classroom->lock;
        $classroom->save();
    }
}
