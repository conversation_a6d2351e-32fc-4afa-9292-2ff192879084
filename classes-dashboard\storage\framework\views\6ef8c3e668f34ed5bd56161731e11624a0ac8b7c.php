
<?php $__env->startSection('content'); ?>
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-12 main-title-flex">
                <h1>Profile Views</h1>
            </div>
        </div>
    </div>
</div>
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="tab-content" id="profileViewsTabContent">
                            <!-- Profile Views Tab -->
                            <div class="tab-pane fade show active" id="profile-views" role="tabpanel" aria-labelledby="profile-views-tab">
                                <table id="profileviews_table" class="table display table-striped table-borderless dt-responsive">
                                    <thead>
                                        <tr>
                                            <th>Class Name</th>
                                            <th>Student Name</th>
                                            <th>Student Email</th>
                                            <th>Viewed At</th>
                                        </tr>
                                    </thead>
                                    <tfoot>
                                        <tr class="search-row">
                                            <th>Class Name</th>
                                            <th>Student Name</th>
                                            <th>Student Email</th>
                                            <th>Viewed At</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
<script>
    var profileviewsRoute = {
        index: "<?php echo e(route('profileviews')); ?>",
    };
</script>
<script src="<?php echo e(asset(mix('js/page-level-js/profileviews/index.js'))); ?>"></script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH G:\UEST\uest_app\uest-app\classes-dashboard\modules/ProfileViews/resources/views/index.blade.php ENDPATH**/ ?>